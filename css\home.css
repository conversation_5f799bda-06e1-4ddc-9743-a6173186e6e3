.hero {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 2em;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  overflow-x: hidden;
}

.hero h1 {
  font-size: clamp(2rem, 15vw, 15rem);
  line-height: 90%;
}


/* home - hero img holder */
.hero-img-holder {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 2em;
}

.hero-img-holder .hero-img {
  position: relative;
  width: 100%;
  height: 100%;
  transform: translateY(-110%) scale(0.25) rotate(0);
  border: 0.3em solid var(--fg);
  border-radius: 2em;
  overflow: hidden;
}

.spacer{
  width: 100vw;
  height: 100svh;
}