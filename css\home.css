.hero {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 2em;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-x: hidden;
}

.hero .hero-header h1 {
  font-size: 20vw;
  line-height: 0.9;
}

.hero .hero-header.hero-header-1 {
  position: relative;
  transform: translateX(-20%);
  z-index: -1;
}

.hero .hero-header.hero-header-2 {
  position: relative;
  transform: translateX(20%);
  z-index: 2;
}

.hero .hero-footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  padding: 2em;
  display: flex;
  justify-content: space-between;
}

.hero .hero-footer .hero-footer-scroll-down {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.hero .hero-footer .hero-footer-symbols {
  height: 1rem;
}

/* home - hero img holder */
.hero-img-holder {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 2em;
}

.hero-img-holder .hero-img {
  position: relative;
  width: 100%;
  height: 100%;
  transform: translateY(-110%) scale(0.25) rotate(0);
  border: 0.3em solid var(--fg);
  border-radius: 2em;
  overflow: hidden;
}
