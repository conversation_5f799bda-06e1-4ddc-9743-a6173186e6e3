
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";     
import SplitText from "gsap/SplitText";

// Import GSAP modules
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { SplitText } from "gsap/SplitText";

// Register plugins
gsap.registerPlugin(ScrollTrigger, SplitText);

// Text Line-by-line Animation
(function() {
  let textAnimations = [];
  const textSplitters = [];

  const doTextLines = () => {
    // Kill existing animations
    textAnimations.forEach(anim => anim.kill());
    textAnimations.length = 0;

    // Clear previous instances
    textSplitters.forEach(splitter => splitter.revert());
    textSplitters.length = 0;

    // Process each text element individually
    document.querySelectorAll("[data-split='lines']").forEach(element => {
      // Split text for animation
      const typeSplit = new SplitText(element, {
        types: "lines",
        linesClass: "lineChild"
      });

      // Create mask for smooth effect
      const maskSplit = new SplitText(element, {
        types: "lines",
        linesClass: "lineParent"
      });

      textSplitters.push(typeSplit, maskSplit);

      // Set initial state
      gsap.set(typeSplit.lines, {
        yPercent: 100,
        opacity: 0
      });

      // Create animation for this element
      const animation = gsap.to(typeSplit.lines, {
        yPercent: 0,
        opacity: 1,
        duration: 0.8,
        stagger: 0.1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: element,
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse",
          markers: false
        }
      });

      textAnimations.push(animation);
    });
  };

  // DOM Ready Event
  document.addEventListener("DOMContentLoaded", () => {
    doTextLines();  // Lines animation with mask
  });

  // Resize event - Optimized debouncing
  let resizeTimeout;
  window.addEventListener("resize", () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      doTextLines();  // Lines animation refresh
    }, 300); // Increased debounce time for better performance
  });
})();
