import gsap from "gsap";
import { ScrambleTextPlugin } from "gsap/ScrambleTextPlugin";
import { ScrollTrigger } from "gsap/ScrollTrigger";


document.addEventListener("DOMContentLoaded", () => {

  gsap.registerPlugin(ScrollTrigger);

  let scrollTriggerInstance = null;

  const initAnimations = () => {
    if (scrollTriggerInstance) {
      scrollTriggerInstance.kill();
    }

    scrollTriggerInstance = ScrollTrigger.create({
      trigger: ".hero-img-holder",
      start: "top bottom",
      end: "top top",
      onUpdate: (self) => {
        const progress = self.progress;
        gsap.set(".hero-img", {
          y: `${-110 + 110 * progress}%`,
          scale: 0.25 + 0.75 * progress,
        });
      },
    });
  };

  initAnimations();

  window.addEventListener("resize", () => {
    initAnimations();
  });
});


// ,,,,,,, 
  document.addEventListener("DOMContentLoaded", () => {
    gsap.registerPlugin(ScrambleTextPlugin);

    const fullText = [
      { el: document.querySelector(".hero h1:nth-child(1)"), text: "URGE" },
      { el: document.querySelector(".hero h1:nth-child(2)"), text: "VISIONARY" },
    ];

    fullText.forEach(({ el, text }) => {
      el.textContent = ""; // Empty first
    });

    // Animate both together like a hacker reveal
    fullText.forEach(({ el, text }) => {
      gsap.fromTo(
        el,
        { scrambleText: { text: "", chars: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", revealDelay: 0, tweenLength: false } },
        {
          scrambleText: {
            text: text,
            chars: ".#123456789-+*-~",
            speed: 1,
            revealDelay: 1,
            tweenLength: false
          },
          duration: 2.5,
          ease: "none"
        }
      );
    });
  });
